import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const [isMoreDropdownOpen, setIsMoreDropdownOpen] = useState(false);
  const [isLearnDropdownOpen, setIsLearnDropdownOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [scrollDirection, setScrollDirection] = useState('up');
  const [dropdownTimeout, setDropdownTimeout] = useState(null);
  const location = useLocation();
  const isHomePage = location.pathname === '/';

  // Handle dropdown closing with delay
  const handleDropdownClose = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
    }
    const timeout = setTimeout(() => {
      setIsMoreDropdownOpen(false);
      setIsLearnDropdownOpen(false);
    }, 150);
    setDropdownTimeout(timeout);
  };

  const handleDropdownOpen = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }
    setIsMoreDropdownOpen(true);
  };

  const handleDropdownStay = () => {
    if (dropdownTimeout) {
      clearTimeout(dropdownTimeout);
      setDropdownTimeout(null);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Determine scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setScrollDirection('down');
        setIsScrolled(true);
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
        if (currentScrollY <= 50) {
          setIsScrolled(false);
        }
      }

      setLastScrollY(currentScrollY);
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (dropdownTimeout) {
        clearTimeout(dropdownTimeout);
      }
    };
  }, [lastScrollY, dropdownTimeout]);

  return (
    <nav className={`navbar ${isScrolled && scrollDirection === 'down' ? 'navbar-scrolled' : ''}`}>
      <div className="nav-container">
        <Link to="/" className="nav-logo">
          <img src="/BullSeed_LOGO.png" alt="BullSeed" className="nav-logo-image" />
          BullSeed
        </Link>

        <ul className="nav-links">
          <li>
            {isHomePage ? (
              <a href="#features">Features</a>
            ) : (
              <Link to="/#features">Features</Link>
            )}
          </li>
          <li>
            {isHomePage ? (
              <a href="#plans">Plans</a>
            ) : (
              <Link to="/#plans">Plans</Link>
            )}
          </li>
          <li>
            {isHomePage ? (
              <a href="#testimonials">Testimonials</a>
            ) : (
              <Link to="/#testimonials">Testimonials</Link>
            )}
          </li>
          <li
            className="nav-dropdown"
            onMouseEnter={handleDropdownOpen}
            onMouseLeave={handleDropdownClose}
          >
            <a
              href="#"
              className="nav-dropdown-trigger"
              onClick={(e) => {
                e.preventDefault();
                setIsMoreDropdownOpen(!isMoreDropdownOpen);
              }}
            >
              More
              <svg
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className={`nav-dropdown-arrow ${isMoreDropdownOpen ? 'open' : ''}`}
              >
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </a>
            {isMoreDropdownOpen && (
              <div
                className="nav-dropdown-menu"
                onMouseEnter={handleDropdownStay}
                onMouseLeave={handleDropdownClose}
              >
                <Link to="/about-us">About Us</Link>
                <Link to="/nft-trade">NFT-Trade</Link>
                <div
                  className="nav-dropdown-submenu"
                  onMouseEnter={() => {
                    handleDropdownStay();
                    setIsLearnDropdownOpen(true);
                  }}
                  onMouseLeave={() => setIsLearnDropdownOpen(false)}
                >
                  <a href="#" className="nav-dropdown-submenu-trigger">
                    Learn
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="nav-dropdown-arrow-right"
                    >
                      <polyline points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </a>
                  {isLearnDropdownOpen && (
                    <div className="nav-dropdown-submenu-content">
                      <a href="/learn/plans">Plans</a>
                      <a href="/learn/faq">FAQ</a>
                    </div>
                  )}
                </div>
                <Link to="/contact">Contact</Link>
              </div>
            )}
          </li>
        </ul>

        <a href="#" className="nav-cta">Start Investing</a>
      </div>
    </nav>
  );
};

export default Navbar;
