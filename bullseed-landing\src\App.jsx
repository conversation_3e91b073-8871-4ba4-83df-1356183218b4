import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import Home from './pages/Home'
import AboutUs from './pages/AboutUs'
import NFTTrade from './pages/NFTTrade'
import Plans from './pages/Plans'
import FAQ from './pages/FAQ'
import Contact from './pages/Contact'

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about-us" element={<AboutUs />} />
          <Route path="/nft-trade" element={<NFTTrade />} />
          <Route path="/learn/plans" element={<Plans />} />
          <Route path="/learn/faq" element={<FAQ />} />
          <Route path="/contact" element={<Contact />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
