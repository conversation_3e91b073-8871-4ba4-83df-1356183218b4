import React from 'react';

const Testimonials = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'Early Crypto Investor',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      text: 'BullSeed\'s automated investment strategies have completely transformed my crypto portfolio. The platform operates 24/7 and I\'ve seen consistent returns without any manual effort.'
    },
    {
      name: '<PERSON>',
      role: 'DeFi Developer',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b332c1c2?w=150&h=150&fit=crop&crop=face',
      text: 'The intelligent automation and portfolio rebalancing features are exceptional. BullSeed\'s AI-powered investment strategies have significantly outperformed my manual approaches.'
    },
    {
      name: '<PERSON>',
      role: 'Crypto Security Expert',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      text: 'The institutional-grade security combined with automated investment management gives me complete peace of mind. BullSeed sets the standard for secure crypto investing.'
    },
    {
      name: '<PERSON>',
      role: 'Investment Portfolio Manager',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      text: 'BullSeed\'s automated portfolio management and real-time analytics have revolutionized how we handle institutional crypto investments. The results speak for themselves.'
    },
    {
      name: 'Michael Thompson',
      role: 'Quantitative Investment Analyst',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      text: 'The AI-driven investment algorithms and automated strategy execution are industry-leading. Our portfolio performance has improved dramatically since using BullSeed.'
    },
    {
      name: 'Lisa Chen',
      role: 'Crypto Fund Manager',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      text: 'BullSeed\'s automated investment platform and intelligent portfolio optimization make it our preferred choice for managing large-scale crypto investments.'
    }
  ];

  return (
    <section className="testimonials" id="testimonials">
      <div className="testimonials-container">
        <div className="testimonials-header">
          <h2 className="testimonials-title">Trusted by Traders</h2>
          <p className="testimonials-subtitle">Join thousands of satisfied traders on CryptoTrade</p>
        </div>

        <div className="testimonials-scroll-container">
          <div className="testimonials-track">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="testimonial-card">
                <div className="testimonial-header">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="testimonial-avatar"
                    onError={(e) => {
                      e.target.src = `https://ui-avatars.com/api/?name=${testimonial.name}&background=00d4aa&color=fff&size=60`;
                    }}
                  />
                  <div className="testimonial-author">
                    <h4 className="testimonial-name">{testimonial.name}</h4>
                    <p className="testimonial-role">{testimonial.role}</p>
                  </div>
                </div>
                <p className="testimonial-text">{testimonial.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
