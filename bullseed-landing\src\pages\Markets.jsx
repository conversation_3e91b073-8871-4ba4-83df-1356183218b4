import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import '../styles/Markets.css';

const Markets = () => {
  const [marketData, setMarketData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('market_cap_desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch real crypto market data from CoinGecko API
  useEffect(() => {
    const fetchMarketData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&order=${sortBy}&per_page=100&page=1&sparkline=true&price_change_percentage=1h,24h,7d`
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch market data');
        }
        
        const data = await response.json();
        setMarketData(data);
        setError(null);
      } catch (error) {
        console.error('Error fetching market data:', error);
        setError('Failed to load market data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchMarketData();
    
    // Update data every 60 seconds
    const interval = setInterval(fetchMarketData, 60000);
    
    return () => clearInterval(interval);
  }, [sortBy]);

  // Filter data based on search term
  const filteredData = marketData.filter(coin =>
    coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    coin.symbol.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatPrice = (price) => {
    if (price < 0.01) {
      return `$${price.toFixed(8)}`;
    } else if (price < 1) {
      return `$${price.toFixed(6)}`;
    } else if (price < 100) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  const formatMarketCap = (marketCap) => {
    if (marketCap >= 1e12) {
      return `$${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `$${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `$${(marketCap / 1e6).toFixed(2)}M`;
    } else {
      return `$${marketCap.toLocaleString()}`;
    }
  };

  const formatVolume = (volume) => {
    if (volume >= 1e9) {
      return `$${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `$${(volume / 1e6).toFixed(2)}M`;
    } else {
      return `$${volume.toLocaleString()}`;
    }
  };

  const formatChange = (change) => {
    if (change === null || change === undefined) return 'N/A';
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(2)}%`;
  };

  const getChangeClass = (change) => {
    if (change === null || change === undefined) return 'neutral';
    return change >= 0 ? 'positive' : 'negative';
  };

  return (
    <div className="markets-page">
      <Navbar />
      
      {/* Hero Section */}
      <section className="markets-hero">
        <div className="markets-hero-container">
          <div className="markets-breadcrumb">
            <a href="/">Home</a>
            <span>/</span>
            <span>Markets</span>
          </div>
          
          <div className="markets-hero-content">
            <div className="markets-hero-badge">
              <span className="markets-hero-badge-icon">📈</span>
              Live Market Data
            </div>
            <h1 className="markets-hero-title">
              Cryptocurrency <span className="highlight">Markets</span>
            </h1>
            <p className="markets-hero-description">
              Real-time cryptocurrency prices, market capitalizations, and trading volumes. 
              Stay informed with live market data to make better investment decisions.
            </p>
          </div>
        </div>
      </section>

      {/* Market Controls */}
      <section className="markets-controls">
        <div className="markets-controls-container">
          <div className="markets-search">
            <div className="search-input-wrapper">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="m21 21-4.35-4.35"/>
              </svg>
              <input
                type="text"
                placeholder="Search cryptocurrencies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
          
          <div className="markets-sort">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="sort-select"
            >
              <option value="market_cap_desc">Market Cap (High to Low)</option>
              <option value="market_cap_asc">Market Cap (Low to High)</option>
              <option value="price_desc">Price (High to Low)</option>
              <option value="price_asc">Price (Low to High)</option>
              <option value="volume_desc">Volume (High to Low)</option>
              <option value="id_asc">Name (A to Z)</option>
            </select>
          </div>
        </div>
      </section>

      {/* Market Data Table */}
      <section className="markets-data">
        <div className="markets-data-container">
          {loading ? (
            <div className="markets-loading">
              <div className="loading-spinner"></div>
              <p>Loading market data...</p>
            </div>
          ) : error ? (
            <div className="markets-error">
              <div className="error-icon">⚠️</div>
              <p>{error}</p>
              <button onClick={() => window.location.reload()} className="retry-button">
                Try Again
              </button>
            </div>
          ) : (
            <div className="markets-table-wrapper">
              <table className="markets-table">
                <thead>
                  <tr>
                    <th className="rank-col">#</th>
                    <th className="name-col">Name</th>
                    <th className="price-col">Price</th>
                    <th className="change-col">1h %</th>
                    <th className="change-col">24h %</th>
                    <th className="change-col">7d %</th>
                    <th className="volume-col">24h Volume</th>
                    <th className="marketcap-col">Market Cap</th>
                    <th className="chart-col">Last 7 Days</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((coin, index) => (
                    <tr key={coin.id} className="market-row">
                      <td className="rank-cell">{coin.market_cap_rank || index + 1}</td>
                      <td className="name-cell">
                        <div className="coin-info">
                          <img 
                            src={coin.image} 
                            alt={coin.name}
                            className="coin-logo"
                            onError={(e) => {
                              e.target.style.display = 'none';
                            }}
                          />
                          <div className="coin-details">
                            <span className="coin-name">{coin.name}</span>
                            <span className="coin-symbol">{coin.symbol.toUpperCase()}</span>
                          </div>
                        </div>
                      </td>
                      <td className="price-cell">{formatPrice(coin.current_price)}</td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_1h_in_currency)}`}>
                        {formatChange(coin.price_change_percentage_1h_in_currency)}
                      </td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_24h)}`}>
                        {formatChange(coin.price_change_percentage_24h)}
                      </td>
                      <td className={`change-cell ${getChangeClass(coin.price_change_percentage_7d_in_currency)}`}>
                        {formatChange(coin.price_change_percentage_7d_in_currency)}
                      </td>
                      <td className="volume-cell">{formatVolume(coin.total_volume)}</td>
                      <td className="marketcap-cell">{formatMarketCap(coin.market_cap)}</td>
                      <td className="chart-cell">
                        {coin.sparkline_in_7d && coin.sparkline_in_7d.price && (
                          <div className="mini-chart">
                            <svg width="100" height="40" viewBox="0 0 100 40">
                              <polyline
                                fill="none"
                                stroke={coin.price_change_percentage_7d_in_currency >= 0 ? "#00d4aa" : "#ef4444"}
                                strokeWidth="2"
                                points={coin.sparkline_in_7d.price
                                  .map((price, i) => `${(i / (coin.sparkline_in_7d.price.length - 1)) * 100},${40 - ((price - Math.min(...coin.sparkline_in_7d.price)) / (Math.max(...coin.sparkline_in_7d.price) - Math.min(...coin.sparkline_in_7d.price))) * 40}`)
                                  .join(' ')}
                              />
                            </svg>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Markets;
